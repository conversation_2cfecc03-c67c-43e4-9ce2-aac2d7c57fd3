import Link from "next/link"
import { Plus, Search, Key, Copy, Eye, MoreHorizontal, Calendar, Users, CheckCircle, XCircle, Clock, Building2, Book<PERSON><PERSON>, Filter } from "lucide-react"
import { verifySession } from "@/app/lib/session"
import { redirect } from "next/navigation"
import prisma from "@/app/lib/prisma"
import Co<PERSON><PERSON>utton from "./CopuButton"

export default async function TokensPage({
  params,
  searchParams
}: {
  params: Promise<{ univId: string }>,
  searchParams: { department?: string, course?: string }
}) {
  // Verify session
  const user = await verifySession();
  if (!user) {
    redirect('/admin/login');
  }

  const { univId } = await params;

  // Verify the university belongs to this admin
  const university = await prisma.univ.findFirst({
    where: {
      id: univId,
      adminId: user.id
    },
    select: {
      id: true,
      name: true
    }
  });

  if (!university) {
    redirect('/admin/all-university');
  }

  // Build where clause for filtering
  const whereClause: any = {
    univId: univId
  };

  if (searchParams.department) {
    whereClause.departmentId = searchParams.department;
  }

  if (searchParams.course) {
    whereClause.courseId = searchParams.course;
  }

  // Fetch instructor tokens
  const tokens = await prisma.instructorToken.findMany({
    where: whereClause,
    include: {
      department: {
        select: { name: true, code: true }
      },
      course: {
        select: { name: true, code: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  });

  // Fetch departments for filter dropdown
  const departments = await prisma.department.findMany({
    where: {
      univId: univId,
      isActive: true
    },
    select: {
      id: true,
      name: true,
      code: true
    },
    orderBy: { name: 'asc' }
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "expired":
        return <Clock className="h-4 w-4 text-orange-600" />
      case "revoked":
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "expired":
        return "bg-orange-100 text-orange-800"
      case "revoked":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-green-600 text-white">
                <Key className="h-4 w-4" />
              </div>
              <span className="text-xl font-bold text-green-600">ExamPro</span>
            </div>
            <div className="flex items-center gap-4">
              <Link href={`/admin/${univId}/dashboard`} className="text-sm text-gray-600 hover:text-gray-900">
                Dashboard
              </Link>
              <Link href={`/admin/${univId}/departments`} className="text-sm text-gray-600 hover:text-gray-900">
                Departments
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Instructor Token Management</h1>
          <p className="text-gray-600">
            Generate and manage access tokens for university instructors in {university.name}
          </p>
        </div>

        {/* Filters and Actions */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search tokens..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full sm:w-80"
                />
              </div>

              <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">All Departments</option>
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name} ({dept.code})
                  </option>
                ))}
              </select>

              <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="expired">Expired</option>
                <option value="revoked">Revoked</option>
              </select>
            </div>

            <Link
              href={`/admin/${univId}/tokens/create`}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4" />
              Generate New Token
            </Link>
          </div>
        </div>

        {/* Tokens Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          {tokens.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Token Code</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Department</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Course</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Usage</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Expiration</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {tokens.map((token) => (
                    <TokenRow key={token.id} token={token} />
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No tokens found</h3>
              <p className="text-sm text-gray-600 mb-4">
                Create your first instructor token to allow teachers to access the examination system.
              </p>
              <Link
                href={`/admin/${univId}/tokens/create`}
                className="inline-flex items-center gap-2 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
              >
                <Plus className="h-4 w-4" />
                Create First Token
              </Link>
            </div>
          )}
        </div>

        {/* Pagination */}
        {tokens.length > 0 && (
          <div className="flex items-center justify-between mt-6">
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">1</span> to <span className="font-medium">{tokens.length}</span> of{" "}
              <span className="font-medium">{tokens.length}</span> results
            </p>
          </div>
        )}
      </main>
    </div>
  )
}

function TokenRow({ token }: { token: any }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "expired":
        return <Clock className="h-4 w-4 text-orange-600" />
      case "revoked":
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "expired":
        return "bg-orange-100 text-orange-800"
      case "revoked":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }



  return (
    <tr className="hover:bg-gray-50">
      <td className="py-4 px-4">
        <div className="flex items-center gap-2">
          <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">{token.code}</code>
          <CopuButton code={token.code} />
        </div>
      </td>
      <td className="py-4 px-4">
        <div className="flex items-center gap-2">
          <Building2 className="h-4 w-4 text-gray-400" />
          <div>
            <p className="font-medium text-gray-900">{token.department.name}</p>
            <p className="text-sm text-gray-500">{token.department.code}</p>
          </div>
        </div>
      </td>
      <td className="py-4 px-4">
        {token.course ? (
          <div className="flex items-center gap-2">
            <BookOpen className="h-4 w-4 text-gray-400" />
            <div>
              <p className="font-medium text-gray-900">{token.course.name}</p>
              <p className="text-sm text-gray-500">{token.course.code}</p>
            </div>
          </div>
        ) : (
          <span className="text-sm text-gray-500">All courses</span>
        )}
      </td>
      <td className="py-4 px-4">
        <div className="flex items-center gap-2">
          <div className="flex-1 bg-gray-200 rounded-full h-2 w-16">
            <div
              className="bg-blue-600 h-2 rounded-full"
              style={{ width: `${(token.usageCount / token.maxUsage) * 100}%` }}
            ></div>
          </div>
          <span className="text-sm text-gray-600">
            {token.usageCount}/{token.maxUsage}
          </span>
        </div>
      </td>
      <td className="py-4 px-4">
        <p className="text-sm text-gray-900 flex items-center gap-1">
          <Calendar className="h-3 w-3" />
          {new Date(token.expirationDate).toLocaleDateString()}
        </p>
      </td>
      <td className="py-4 px-4">
        <span
          className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(token.status)}`}
        >
          {getStatusIcon(token.status)}
          {token.status.charAt(0).toUpperCase() + token.status.slice(1)}
        </span>
      </td>
      <td className="py-4 px-4">
        <div className="flex items-center gap-2">
          <button className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded">
            <Eye className="h-4 w-4" />
          </button>
          <CopuButton code={token.code} />
          <button className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded">
            <MoreHorizontal className="h-4 w-4" />
          </button>
        </div>
      </td>
    </tr>
  )
}
