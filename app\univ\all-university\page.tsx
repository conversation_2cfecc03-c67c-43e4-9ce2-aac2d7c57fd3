import Link from "next/link"
import { BookOpen, Search, MapPin, Users, GraduationCap, ChevronRight, Building } from "lucide-react"
import prisma from "@/app/lib/prisma"

type UnivType = { id: string; name: string; location: string; students: number; verified: string }
const enrichUniversities = (universities: UnivType[]) => {
const tailwindColors = [
  "bg-red-600", "bg-red-700", "bg-gray-800", "bg-blue-600", "bg-blue-800",
  "bg-orange-600", "bg-blue-700", "bg-red-800", "bg-orange-700", "bg-green-600",
  "bg-purple-700", "bg-teal-600", "bg-yellow-500"
];

function generateLogo(name: string): string {
  const words = name.split(" ");
  if (words.length === 1) return words[0].slice(0, 3).toUpperCase();
  return words.slice(0, 2).map(word => word[0].toUpperCase()).join("");
}

function getRandomColor(): string {
  return tailwindColors[Math.floor(Math.random() * tailwindColors.length)];
}

return universities.map(univ => ({
  ...univ,
  logo: generateLogo(univ.name),
  color: getRandomColor()
}));
}
export default async function UniversitySelectionPage() {
  const unUnrich_universities = await prisma.univ.findMany({ select: { id: true, name: true, location: true, students: true, verified: true }})
  const comPleteUniversity = unUnrich_universities.map((univ) => ({ ...univ, students: univ.students.length }))
  const universities = enrichUniversities(comPleteUniversity)
  
  return (
    <div className="min-h-screen w-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded bg-green-600 text-white">
                <BookOpen className="h-4 w-4" />
              </div>
              <span className="text-xl font-bold text-green-600">ExamPro</span>
            </div>
            <div className="flex items-center gap-4">
              <Link href="/help" className="text-sm text-gray-600 hover:text-gray-900">
                Need help?
              </Link>
              <Link href="/contact" className="text-sm font-medium text-green-600 hover:text-green-700">
                Contact Support
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className=" mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Choose Your University</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Select your institution to access ExamPro's online examination platform
          </p>
        </div>

        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-md mx-auto">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search universities..."
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
        </div>

        {/* University Grid */}
        <div className="grid w-full min-w-full items-center  gap-4 md:grid-cols-2 lg:grid-cols-3 mb-12">
          {universities.map((university) => (
            <UniversityCard key={university.id} university={university} />
          ))}
        </div>

        {/* Can't find university */}
        <div className="text-center">
          <div className="bg-white rounded-lg border border-gray-200 p-6 max-w-md mx-auto">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Can't find your university?</h3>
            <p className="text-sm text-gray-600 mb-4">
              We're constantly adding new institutions. Request your university to be added.
            </p>
            <button className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors text-sm font-medium">
              Request University
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}

function UniversityCard({ university }: { university: any }) {
  return (
    <div className="bg-white rounded-lg border min-w-[400px] border-gray-200 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer group">
      <div className="p-6">
        <div className="flex items-start gap-4">
          <div
            className={`flex h-12 w-12 items-center justify-center rounded-lg text-white text-sm font-bold ${university.color}`}
          >
            {university.logo}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                  {university.name}
                </h3>
                <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                  <MapPin className="h-3 w-3" />
                  {university.location}
                </div>
                <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                  <Users className="h-3 w-3" />
                  {university.students} students
                </div>
              </div>
              {university.verified == "Accepted" && (
                <div className="flex items-center gap-1 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  Verified
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Role Selection Buttons */}
        <div className="mt-6 grid grid-cols-2 gap-3">
          <Link
            href={`/univ/${university.id}/st/login`}
            className="flex items-center justify-center gap-2 bg-blue-50 text-blue-700 py-2 px-3 rounded-md hover:bg-blue-100 transition-colors text-sm font-medium group"
          >
            <GraduationCap className="h-4 w-4" />
            Student
            <ChevronRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
          </Link>
          <Link
            href={`/teacher/${university.id}/login`}
            className="flex items-center justify-center gap-2 bg-green-50 text-green-700 py-2 px-3 rounded-md hover:bg-green-100 transition-colors text-sm font-medium group"
          >
            <BookOpen className="h-4 w-4" />
            Instructor
            <ChevronRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
          </Link>
        </div>
      </div>
    </div>
  )
}
