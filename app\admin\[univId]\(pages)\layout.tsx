import React from 'react'
import SideBar from './Sidebar'
import { Bell, LogOut, Search } from 'lucide-react'
import MenuButton from './dashboard/MenuButton'
import SideBarOverlay from './dashboard/SideBarOverlay'

type Props = {
    children:React.ReactNode
    params:Promise<{univId:string}>
}

const layout = async ({params,children}: Props) => {
  const {univId}  = await params
  return (
    <div className="flex min-h-screen bg-gray-50">
        <SideBar univId={univId}/>
        <div className="flex-1 lg:ml-0">
                {/* Top Navbar */}
                <header className="bg-white shadow-sm border-b">
                  <div className="flex items-center justify-between h-16 px-6">
                    <div className="flex items-center gap-4">
                      <MenuButton/>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="text"
                          placeholder="Search..."
                          className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>
        
                    <div className="flex items-center gap-4">
                      <button className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md">
                        <Bell className="h-5 w-5" />
                        <span className="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"></span>
                      </button>
        
                      <div className="flex items-center gap-3">
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">Admin User</p>
                          <p className="text-xs text-gray-500">System Administrator</p>
                        </div>
                        <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">AU</span>
                        </div>
                        <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md">
                          <LogOut className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </header>
                {children}
                </div>
                {/* Sidebar Overlay */}
      <SideBarOverlay />
    </div>
  )
}

export default layout