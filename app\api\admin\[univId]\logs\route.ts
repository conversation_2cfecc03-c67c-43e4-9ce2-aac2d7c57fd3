import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { verifySession } from '@/app/lib/session';

// GET: Fetch logs for a university
export async function GET(req: NextRequest, { params }: { params: { univId: string } }) {
  const user = await verifySession();
  if (!user) {
    return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }
  const { univId } = params;
  try {
    const logs = await prisma.log.findMany({
      where: { univId },
      orderBy: { timestamp: 'desc' },
      take: 100,
    });
    return NextResponse.json({ success: true, logs });
  } catch (error) {
    return NextResponse.json({ success: false, error: 'Failed to fetch logs' }, { status: 500 });
  }
}

// POST: Create a new log entry
export async function POST(req: NextRequest, { params }: { params: { univId: string } }) {
  const user = await verifySession();
  if (!user) {
    return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
  }
  const { univId } = params;
  const body = await req.json();
  try {
    const log = await prisma.log.create({
      data: {
        univId,
        timestamp: body.timestamp ? new Date(body.timestamp) : new Date(),
        action: body.action,
        performedBy: body.performedBy,
        affectedUser: body.affectedUser,
        ipAddress: body.ipAddress,
        details: body.details,
        severity: body.severity,
      },
    });
    return NextResponse.json({ success: true, log });
  } catch (error) {
    return NextResponse.json({ success: false, error: 'Failed to create log' }, { status: 500 });
  }
}
