generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Univ{
id String @id @default(cuid())
name String
domain String @unique
students Student[]
admin     Admin  @relation(fields: [adminId], references: [id])
adminId   String   @unique
instructors Instructor[]
location String
verified String @default("Pending")
// Additional fields for comprehensive university data
logoUrl String?
address String?
city String?
state String?
country String?
zipCode String?
contactEmail String?
contactPhone String?
website String?
universityType String @default("public")
establishedYear Int?
studentCapacity Int?
description String?
allowSelfRegistration Boolean @default(false)
requireEmailVerification Boolean @default(true)
enableMultipleCampuses Boolean @default(false)
departments Department[]
instructorTokens InstructorToken[]
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt

logs Log[]
}


model Instructor{
  id String @id @default(cuid())
  name String
  email String @unique
  password String
  univ Univ[]
  students Student[] @relation("InstructorToStudent")
  exams Exam[]
  courses Course[] // New: one-to-many relation to Course
}



model Admin {
  id            String         @id @default(cuid())
  name          String
  email         String         @unique
  password      String
  phoneNumber   String
  univ          Univ?          @relation
  subscriptions Subscription[] @relation("UserSubscriptions")
  payments      Payment[]      @relation("UserPayments")
}


model Student{
id String @id @default(cuid())
name String
email String @unique
univId String @unique
password String
univ Univ @relation(fields: [univId],references: [id])
instructor   Instructor[] @relation("InstructorToStudent")
examSessions StudentExamSession[] // Many-to-many relation
examEnrollments ExamEnrollment[] // Opposite relation field for ExamEnrollment
status String @default("active") // "active", "blocked", "inactive"
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
lastLogin DateTime
joinDate DateTime @default(now())
examsCompleted Int @default(0)
averageScore Float @default(0)
}


model TokenPayment{
  email String @unique
  payementToken String @unique 
}


model Plan {
  id            String         @id @default(cuid())
  name          String         @unique
  price         Float
  currency      String         @default("XAF")
  billing       String         // "monthly" or "yearly"
  features      String[]
  isPopular     Boolean        @default(false)
  savings       Float?
  isActive      Boolean        @default(true)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  subscriptions Subscription[]
  payments      Payment[]
}

model Subscription {
  id                 String   @id @default(cuid())
  adminId            String
  planId             String?
  paymentId          String? @unique
  status             String   @default("unactive")
  activeDate         String   
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  payment Payment?   @relation(fields:[paymentId],references:[id],onDelete: Restrict)
  admin Admin @relation("UserSubscriptions", fields: [adminId], references: [id], onDelete: Cascade)
  plan  Plan?  @relation(fields: [planId], references: [id], onDelete: Restrict)

  @@unique([adminId, planId,paymentId])
}



model Payment {
  id              String   @id @default(cuid())
  adminId         String?
  planId          String?
  email           String
  amount          Float
  currency        String   @default("XAF")
  method          String
  status          String   @default("pending")
  transactionId   String?  @unique
  gatewayResponse Json?
  token           String   @unique
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  subscription   Subscription?
  admin Admin? @relation("UserPayments", fields: [adminId], references: [id], onDelete: SetNull)
  plan  Plan?  @relation(fields: [planId], references: [id], onDelete: SetNull)
}



model RenewalReminder {
  id             String   @id @default(cuid())
  adminId         String
  subscriptionId String
  reminderType   String   // "7_days", "3_days", "1_day", "expired"
  sentAt         DateTime @default(now())
  emailSent      Boolean  @default(false)
  smsSent        Boolean  @default(false)
  createdAt      DateTime @default(now())
}

model Coupon {
  id        String    @id @default(uuid())
  code      String    @unique
  discount  Float 
  type      String    @default("percentage") 
  expiresAt DateTime?
  isActive  Boolean   @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}


model Tokens {
  id String @unique @id @default(cuid())
  token String
}

model Department {
  id String @id @default(cuid())
  name String
  description String?
  code String // e.g., "CS", "MATH", "PHYS"
  headOfDepartment String?
  email String?
  phone String?
  location String?
  establishedYear Int?
  isActive Boolean @default(true)

  // Relations
  univId String
  univ Univ @relation(fields: [univId], references: [id], onDelete: Cascade)
  courses Course[]
  instructorTokens InstructorToken[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([univId, code])
}

model Course {
  id String @id @default(cuid())
  name String
  code String // e.g., "CS101", "MATH201"
  description String?
  credits Int @default(3)
  semester String? // "Fall", "Spring", "Summer"
  year Int?
  isActive Boolean @default(true)

  // Relations
  departmentId String
  department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)
  instructorTokens InstructorToken[]

  instructorId String // New: relates course to a particular instructor
  instructor Instructor @relation(fields: [instructorId], references: [id], onDelete: Cascade)

  exams Exam[] // New: one-to-many relation to Exam

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([departmentId, code])
}

model InstructorToken {
  id String @id @default(cuid())
  code String @unique // Generated token code

  // Relations
  univId String
  univ Univ @relation(fields: [univId], references: [id], onDelete: Cascade)

  departmentId String
  department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  courseId String?
  course Course? @relation(fields: [courseId], references: [id], onDelete: SetNull)

  // Token details
  maxUsage Int @default(50)
  usageCount Int @default(0)
  expirationDate DateTime
  status String @default("active") // "active", "expired", "revoked"

  // Metadata
  createdBy String // Admin ID or name
  notes String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Log {
  id           String   @id @default(cuid())
  univId       String
  timestamp    DateTime @default(now())
  action       String
  performedBy  String
  affectedUser String
  ipAddress    String
  details      Json
  severity     String   // "high", "medium", "low"
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  univ         Univ     @relation(fields: [univId], references: [id], onDelete: Cascade)
}

model Exam {
  id           String   @id @default(cuid())
  title        String
  description  String
  duration     Int
  startDate    DateTime
  startTime    String
  instructorId String
  instructor   Instructor @relation(fields: [instructorId], references: [id], onDelete: Cascade)
  courseId     String // New: relates exam to a particular course
  course       Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  sessions     ExamSession[]
  questions    Question[]
  enrollments  ExamEnrollment[] // Opposite relation field
}

model Question {
  id        String   @id @default(cuid())
  text      String
  type      String
  required  Boolean
  options   Json
  saved     Boolean @default(false)
  savedAt   DateTime?
  examId    String
  exam      Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  answers   Answers? // Opposite relation field for Answers
}

model ExamSession {
  id        String   @id @default(cuid())
  examId    String
  exam      Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)
  students  StudentExamSession[] // Many-to-many relation
  status    String   // 'active', 'completed', etc.
  score     Float?
  startedAt DateTime @default(now())
  endedAt   DateTime?
}

model StudentExamSession {
  studentId      String
  examSessionId  String
  student        Student     @relation(fields: [studentId], references: [id], onDelete: Cascade)
  examSession    ExamSession @relation(fields: [examSessionId], references: [id], onDelete: Cascade)
  grade          Float?
  timeTaken      Int? // Time taken in seconds
  status             ExamStatus @default(IN_PROGRESS)
  violationReportId  String?   @unique
  startTime      DateTime @default(now())
  endTime        DateTime?
  answers       Answers[] // New: relation to store answers for the exam session
  // Optional: Add a field to track the exam session's status
  // Relations
  violationReport    ExamViolationReport? @relation(fields: [violationReportId], references: [id])
  @@id([studentId, examSessionId])
}

model ExamEnrollment {
  id         String   @id @default(cuid())
  studentId  String
  examId     String
  status     String?   @default("pending") // "pending", "approve", "rejecte", "cancelled"
  enrolledAt DateTime @default(now())

  student    Student  @relation(fields: [studentId], references: [id], onDelete: Cascade)
  exam       Exam     @relation(fields: [examId], references: [id], onDelete: Cascade)

  @@unique([studentId, examId]) // Prevent duplicate enrollments
}

model ExamViolationReport {
  id              String    @id @default(cuid())
  studentName     String
  studentId       String
  examId          String
  examTitle       String
  violationType   String    // The final violation that triggered the block
  timestamp       DateTime  // When the final violation occurred
  examDuration    Int       // Duration in seconds
  totalViolations Int       // Total number of violations
  violationPoints Int       // Total violation points accumulated
  allViolations   String    // JSON string of all violations
  userAgent       String?   // Browser/device information
  screenResolution String?  // Screen resolution
  ipAddress       String?   // IP address of the student
  blockedAt       DateTime  // When the exam was blocked
  reportedAt      DateTime  @default(now()) // When the report was created
  status          ExamStatus @default(BLOCKED)
  
  // Relations
  violations      ExamViolation[]
  examSession     StudentExamSession?
  
  // Indexes for better query performance
  @@index([studentId])
  @@index([examId])
  @@index([reportedAt])
  @@index([examId, studentId])
  
  @@map("exam_violation_reports")
}

model ExamViolation {
  id             String    @id @default(cuid())
  reportId       String
  violationType  String
  timestamp      DateTime
  severity       ViolationSeverity
  sequenceNumber Int       // Order of violation occurrence
  
  // Relations
  report         ExamViolationReport @relation(fields: [reportId], references: [id], onDelete: Cascade)
  
  @@index([reportId])
  @@map("exam_violations")
}

// Enums
enum ExamStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  BLOCKED
  CANCELLED
}

enum ViolationSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// Optional: Admin notification log
model AdminNotification {
  id          String    @id @default(cuid())
  type        NotificationType
  title       String
  message     String
  examId      String?
  studentId   String?
  isRead      Boolean   @default(false)
  sentAt      DateTime  @default(now())
  readAt      DateTime?
  
  @@index([isRead])
  @@index([sentAt])
  @@map("admin_notifications")
}

enum NotificationType {
  VIOLATION_ALERT
  EXAM_BLOCKED
  SYSTEM_ERROR
  GENERAL
}


model Answers{
  id String @id @default(cuid())
  questionId String @unique
  question Question @relation(fields: [questionId], references: [id], onDelete: Cascade)
  answer String[] // Single answer or multiple answers for multiple-choice questions
  studentId String
  examSessionId String
  studentExamSession StudentExamSession @relation(fields: [studentId, examSessionId], references: [studentId, examSessionId], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([questionId, studentId, examSessionId]) // Prevent duplicate answers for the same question in a session
}