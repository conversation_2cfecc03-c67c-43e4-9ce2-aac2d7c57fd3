"use client"
import { Subscription } from '@/app/generated/prisma'
import { createToken } from '@/app/lib/token'
import { Calendar, ChevronRight } from 'lucide-react'
import { redirect } from 'next/navigation'
import { useRouter } from 'next/router'
import React from 'react'
import { toast } from 'sonner'

type Props = {
    subscription:Subscription
    user:{
        id:string,
        email:string
    }
}
const ButtonClick = ({subscription,user}:Props) => {

const handleCreate = async ({subscriptionId,user}:{subscriptionId:string,user:{id:string,email:string}}) => {
  const res = await fetch('/api/subscriptions/token',{
    method:'POST',
    headers: { 'Content-Type': 'application/json' },
    body:JSON.stringify({subscriptionId,user})
  })
  if(!res.ok){
    toast.error('Something went wrong')
    return null  
}
  redirect('/admin/create-univesity')
  return null
}

return(
  <button
            onClick={() => handleCreate({subscriptionId:subscription.id,user})}
              className="flex items-center justify-center gap-2 bg-gray-50 text-gray-700 py-2 px-3 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium group"
            >
              <Calendar className="h-4 w-4" />
              User this subscription
              <ChevronRight className="h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
            </button>
)

}

export default ButtonClick