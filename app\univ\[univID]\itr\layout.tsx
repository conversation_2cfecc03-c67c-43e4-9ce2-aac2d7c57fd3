import type { Metadata } from 'next'
import './globals.css'
import { verifyInstructorSession } from '@/app/lib/instructor-session'
import { redirect } from 'next/navigation'

export const metadata: Metadata = {
  title: 'Instructor',
  description: 'Created with v0',
  generator: 'v0.dev',
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const instructor = await verifyInstructorSession()
  if(!instructor) redirect('/')
  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}
